import { ColumnDef, TableOptions } from '@tanstack/react-table'
import { ITableProps } from '..'

export const transformTableProps = ({
  // columns,
  ...props
}: ITableProps<any>): Partial<TableOptions<any>> => {
  return {
    ...props,
    // columns:
    //   columns?.map(
    //     (column) =>
    //       ({
    //         ...column,
    //         accessorKey: column.dataIndex,
    //         id: column.key,
    //         header: column.title,
    //         cell: ({ row, column: col, getValue }) => {
    //               const value = getValue()
    //               return column.render?.({
    //                 value,
    //                 data: row.original,
    //                 setData: (data) => row.setData(data),
    //                 dataIndex: col.id,
    //                 rowIdx: row.index,
    //               }) || value
    //             },
    //       } as ColumnDef<any>)
    //   ) || [],
    data: props.dataSource || [],
  } as Partial<TableOptions<any>>
}
