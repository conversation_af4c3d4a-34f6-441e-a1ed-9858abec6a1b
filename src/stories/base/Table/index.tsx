import React, { FC, useMemo } from 'react'
import classNames from 'classnames'
import './index.scss'
import { transformTableProps } from './utils'
// @ts-ignore
import { TableColumn } from './interface'
import { useVirtualizer } from '@tanstack/react-virtual'
import { TableContainer } from './components/TableContainer'
import { useTableInstance } from './hooks'

// @ts-ignore
export * from './interface'

export interface ITableProps<RecordType> {
  columns: TableColumn<RecordType, keyof RecordType>[]
  dataSource?: RecordType[]
  pinned?: [number, number?]
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
  // 分组相关props
  grouping?: string[]
  onGroupingChange?: (grouping: string[]) => void
  enableGrouping?: boolean
  useGroupingMode?: boolean // true: 使用TableContainer, false: 使用虚拟化
}

const preCls = 'tita-ui-tan-stack-table'

export const Table: FC<ITableProps<any>> = React.memo(
  ({
    className,
    style,
    pinned,
    grouping = [],
    onGroupingChange,
    enableGrouping = false,
    useGroupingMode = false,
    ...other
  }) => {
    const props = transformTableProps(other)

    // 使用封装好的 useTableInstance
    const table = useTableInstance(
      props.data || [],
      props.columns || [],
      grouping,
      [], // columnOrder - 可以后续扩展
      onGroupingChange || (() => {}), // setGrouping
      () => {}  // setColumnOrder - 可以后续扩展
    )
    const { rows } = table.getRowModel()

    // 如果启用分组模式，直接使用 TableContainer
    if (useGroupingMode) {
      return (
        <div className={classNames('overflow-x-auto', className)} style={style}>
          <TableContainer table={table} grouping={grouping} rows={rows} />
        </div>
      )
    }
    const visibleColumns = table.getVisibleLeafColumns()
    const tableContainerRef = React.useRef<HTMLDivElement>(null)

    const columnVirtualizer = useVirtualizer<
      HTMLDivElement,
      HTMLTableCellElement
    >({
      count: visibleColumns.length,
      estimateSize: (index) => visibleColumns[index].getSize(), //estimate width of each column for accurate scrollbar dragging
      getScrollElement: () => tableContainerRef.current,
      horizontal: true,
      overscan: 3, //how many columns to render on each side off screen each way (adjust this for performance)
    })
    const virtualColumns = columnVirtualizer.getVirtualItems()
    let virtualPaddingLeft: number | undefined
    let virtualPaddingRight: number | undefined

    // 左侧固定列的宽度
    const pinLeftColumnSize = useMemo(() => {
      const pinnedLeftColumns = visibleColumns.slice(0, pinned?.[0] ?? 0)
      return pinnedLeftColumns.reduce(
        (total, column) => total + column.getSize(),
        0
      )
    }, [pinned, visibleColumns])

    // 右侧固定列的宽度
    const pinRightColumnSize = useMemo(() => {
      const pinnedRightColumns = visibleColumns.slice(pinned?.[1] ?? 0)
      return pinnedRightColumns.reduce(
        (total, column) => total + column.getSize(),
        0
      )
    }, [pinned, visibleColumns])

    if (columnVirtualizer && virtualColumns?.length) {
      virtualPaddingLeft = virtualColumns[0]?.start ?? 0 - pinLeftColumnSize
      virtualPaddingRight =
        columnVirtualizer.getTotalSize() -
        (virtualColumns[virtualColumns.length - 1]?.end ?? 0) -
        pinRightColumnSize
    }

    return (
      <div
        className={classNames(preCls, className)}
        ref={tableContainerRef}
        style={{
          height: '800px', //should be a fixed height
          ...style,
          overflow: 'auto', //our scrollable table container
          position: 'relative', //needed for sticky header
        }}
      >
        <TableContainer table={table} grouping={[]} rows={rows} />
      </div>
    )
  }
)

export default Table
