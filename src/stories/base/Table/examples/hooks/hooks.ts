import React from 'react'
import {
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { TaskData } from '../interface'
import { defaultData } from '../group/data'
import { columns } from '../group/columns'

// ==================== 业务逻辑 Hook ====================
export const useTableState = () => {
  const [data] = React.useState<TaskData[]>(() => [...defaultData])
  const [grouping, setGrouping] = React.useState<string[]>(['status', 'priority'])
  const [columnOrder, setColumnOrder] = React.useState<string[]>(
    columns.map((column) => column.id || '')
  )

  const toggleGrouping = (columnId: string) => {
    setGrouping((prevGrouping) => {
      if (prevGrouping.includes(columnId)) {
        return prevGrouping.filter((id) => id !== columnId)
      }
      return [...prevGrouping, columnId]
    })
  }

  return {
    data,
    grouping,
    setGrouping,
    columnOrder,
    setColumnOrder,
    toggleGrouping,
  }
}

export const useTableInstance = (
  data: TaskData[], 
  grouping: string[], 
  columnOrder: string[], 
  setGrouping: any, 
  setColumnOrder: any
) => {
  return useReactTable({
    data,
    columns,
    state: {
      grouping,
      columnOrder,
    },
    groupedColumnMode: false,
    onGroupingChange: setGrouping,
    onColumnOrderChange: setColumnOrder,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
  })
}

// 获取列标题的业务逻辑
export const getColumnHeader = (colId: string) => {
  const column = columns.find((col) => col.id === colId)
  return typeof column?.header === 'string' ? column.header : colId
}
