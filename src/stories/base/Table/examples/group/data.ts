import { TaskData } from '../interface'

// ==================== 基础数据模板 ====================
const taskNames = [
  '确定考核周期', '明确考核目的', '选择考核方法', '制定考核标准',
  '设计登录页面', '后端 API 开发', '修复支付 Bug', 'UI/UX 设计评审',
  '部署到测试环境', '用户需求分析', '数据库设计', '前端组件开发',
  '性能优化', '安全性测试', '代码审查', '文档编写',
  '集成测试', '用户验收测试', '线上监控', 'Bug 修复',
  '新功能开发', '系统重构', '技术调研', '架构设计',
  '移动端适配', '国际化支持', '缓存优化', '接口联调',
  '第三方集成', '数据迁移', '备份恢复', '权限管理'
]

const statuses = ['进行中', '已完成', '待办', '已暂停', '已取消']

const assigneeNames = [
  '况民振', '鱼小溪', '军', '关超', '张三', '李四', '王五', '赵六', '孙七',
  '陈八', '周九', '吴十', '郑一', '冯二', '褚三', '卫四', '蒋五', '沈六',
  '韩七', '杨八', '朱九', '秦十', '尤一', '许二', '何三', '吕四', '施五',
  '张六', '孔七', '曹八', '严九', '华十', '金一', '魏二', '陶三', '姜四'
]

const priorities = ['P1 最高', 'P2 高', 'P3 中', 'P4 低']

const teams = ['核心团队', '支持团队', '设计团队', '运维团队', '测试团队', '产品团队']

// ==================== 生成函数 ====================
function generateMockData(count: number): TaskData[] {
  const data: TaskData[] = []
  
  for (let i = 0; i < count; i++) {
    const taskName = taskNames[Math.floor(Math.random() * taskNames.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const assigneeName = assigneeNames[Math.floor(Math.random() * assigneeNames.length)]
    const priority = priorities[Math.floor(Math.random() * priorities.length)]
    const team = teams[Math.floor(Math.random() * teams.length)]
    
    data.push({
      taskName: `${taskName} ${i + 1}`,
      status,
      assignee: {
        name: assigneeName,
        avatar: `https://i.pravatar.cc/150?u=a${(i % 100).toString().padStart(2, '0')}`
      },
      priority,
      team,
    })
  }
  
  return data
}

// ==================== 原始数据 ====================
export const originalData: TaskData[] = [
  {
    taskName: '确定考核周期',
    status: '进行中',
    assignee: { name: '况民振', avatar: 'https://i.pravatar.cc/150?u=a01' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '明确考核目的',
    status: '进行中',
    assignee: { name: '鱼小溪', avatar: 'https://i.pravatar.cc/150?u=a02' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '选择考核方法',
    status: '进行中',
    assignee: { name: '军', avatar: 'https://i.pravatar.cc/150?u=a03' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '制定考核标准',
    status: '进行中',
    assignee: { name: '关超', avatar: 'https://i.pravatar.cc/150?u=a04' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '设计登录页面',
    status: '已完成',
    assignee: { name: '张三', avatar: 'https://i.pravatar.cc/150?u=a05' },
    priority: 'P2 高',
    team: '核心团队',
  },
  {
    taskName: '后端 API 开发',
    status: '已完成',
    assignee: { name: '李四', avatar: 'https://i.pravatar.cc/150?u=a06' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '修复支付 Bug',
    status: '待办',
    assignee: { name: '王五', avatar: 'https://i.pravatar.cc/150?u=a07' },
    priority: 'P1 最高',
    team: '支持团队',
  },
  {
    taskName: 'UI/UX 设计评审',
    status: '待办',
    assignee: { name: '赵六', avatar: 'https://i.pravatar.cc/150?u=a08' },
    priority: 'P3 中',
    team: '设计团队',
  },
  {
    taskName: '部署到测试环境',
    status: '已完成',
    assignee: { name: '孙七', avatar: 'https://i.pravatar.cc/150?u=a09' },
    priority: 'P4 低',
    team: '运维团队',
  },
]

// ==================== 一万条模拟数据 ====================
export const defaultData: TaskData[] = generateMockData(10000)