import { TaskData } from './types'

// ==================== 基础数据模板 ====================
const baseTaskNames = [
  '确定考核周期', '明确考核目的', '选择考核方法', '制定考核标准',
  '设计登录页面', '后端 API 开发', '修复支付 Bug', 'UI/UX 设计评审',
  '部署到测试环境', '前端组件开发', '数据库优化', '性能测试',
  '安全审计', '代码重构', '文档编写', '用户测试',
  '集成测试', '单元测试', '需求分析', '产品规划'
]

const statuses = ['进行中', '已完成', '待办', '暂停', '取消']

const names = [
  '况民振', '鱼小溪', '军', '关超', '张三', '李四', '王五', '赵六', '孙七',
  '周八', '吴九', '郑十', '刘一', '陈二', '杨三', '黄四', '朱五', '林六',
  '徐七', '何八', '马九', '罗十', '高一', '梁二', '宋三', '唐四', '许五',
  '韩六', '冯七', '邓八', '曹九', '彭十', '曾一', '萧二', '田三', '董四'
]

const priorities = ['P1 最高', 'P2 高', 'P3 中', 'P4 低']

const teams = ['核心团队', '支持团队', '设计团队', '运维团队', '测试团队', '产品团队']

// ==================== 生成随机数据的工具函数 ====================
const getRandomItem = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)]

const generateRandomTaskData = (index: number): TaskData => {
  const taskName = `${getRandomItem(baseTaskNames)} - ${index + 1}`
  
  return {
    taskName,
    status: getRandomItem(statuses),
    assignee: {
      name: getRandomItem(names),
      avatar: `https://i.pravatar.cc/150?u=a${String(index).padStart(4, '0')}`
    },
    priority: getRandomItem(priorities),
    team: getRandomItem(teams),
  }
}

// ==================== 原始数据 ====================
export const defaultData: TaskData[] = [
  {
    taskName: '确定考核周期',
    status: '进行中',
    assignee: { name: '况民振', avatar: 'https://i.pravatar.cc/150?u=a01' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '明确考核目的',
    status: '进行中',
    assignee: { name: '鱼小溪', avatar: 'https://i.pravatar.cc/150?u=a02' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '选择考核方法',
    status: '进行中',
    assignee: { name: '军', avatar: 'https://i.pravatar.cc/150?u=a03' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '制定考核标准',
    status: '进行中',
    assignee: { name: '关超', avatar: 'https://i.pravatar.cc/150?u=a04' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '设计登录页面',
    status: '已完成',
    assignee: { name: '张三', avatar: 'https://i.pravatar.cc/150?u=a05' },
    priority: 'P2 高',
    team: '核心团队',
  },
  {
    taskName: '后端 API 开发',
    status: '已完成',
    assignee: { name: '李四', avatar: 'https://i.pravatar.cc/150?u=a06' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '修复支付 Bug',
    status: '待办',
    assignee: { name: '王五', avatar: 'https://i.pravatar.cc/150?u=a07' },
    priority: 'P1 最高',
    team: '支持团队',
  },
  {
    taskName: 'UI/UX 设计评审',
    status: '待办',
    assignee: { name: '赵六', avatar: 'https://i.pravatar.cc/150?u=a08' },
    priority: 'P3 中',
    team: '设计团队',
  },
  {
    taskName: '部署到测试环境',
    status: '已完成',
    assignee: { name: '孙七', avatar: 'https://i.pravatar.cc/150?u=a09' },
    priority: 'P4 低',
    team: '运维团队',
  },
]

// ==================== 生成1万条数据 ====================
export const largeData: TaskData[] = Array.from({ length: 10000 }, (_, index) => 
  generateRandomTaskData(index)
)

// ==================== 导出合并数据 ====================
export const allData: TaskData[] = [...defaultData, ...largeData]