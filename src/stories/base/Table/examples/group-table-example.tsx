// 重构后的代码结构：
// 纯组件层 (src/components/table/):
// - types.ts - 纯组件类型定义
// - utils.ts - 工具函数
// - GroupingControls.tsx - 分组控制组件
// - TableHeader.tsx - 表头组件
// - CellBorder.tsx - 单元格边框组件
// - LevelIndicators.tsx - 层级指示线组件
// - GroupEndRows.tsx - 分组结束行组件
// - DataRowEndSpacing.tsx - 数据行结束间距组件
// - GroupRow.tsx - 分组行组件
// - DataRow.tsx - 数据行组件
// - TableRow.tsx - 表格行组件
// - TableBody.tsx - 表体组件
// - TableContainer.tsx - 表格容器组件
// - index.ts - 纯组件导出文件

// 业务层 (src/stories/base/Table/business/):
// - types.ts - 业务类型定义
// - data.ts - 业务数据
// - cell-renderers.tsx - 业务单元格渲染函数
// - columns.ts - 业务列配置
// - hooks.ts - 业务逻辑 Hook
// - GroupableTaskTable.tsx - 业务主组件
// - index.ts - 业务层导出文件

import GroupableTaskTable from './GroupableTaskTable'

// ==================== 示例组件 ====================
function GroupTableExample() {
  return <GroupableTaskTable />
}

export default GroupTableExample
