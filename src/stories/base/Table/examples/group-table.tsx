import { useTableState, useTableInstance, getColumnHeader } from '@/stories/base/Table/hooks'
import { GroupingControls } from '@/components/table/GroupingControls'
import { TableContainer } from '@/stories/base/Table/components/TableContainer'
import { columns } from './group/columns'
import { defaultData } from './group/data'

// ==================== 主组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, columnOrder, setColumnOrder, toggleGrouping } = useTableState(defaultData, columns)
  const table = useTableInstance(data, columns, grouping, columnOrder, setGrouping, setColumnOrder)
  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      1231
      <GroupingControls
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
        getColumnHeader={(colId: string) => getColumnHeader(colId, columns)}
      />
      <TableContainer table={table} grouping={grouping} rows={rows} />
    </div>
  )
}

export default GroupableTaskTable
