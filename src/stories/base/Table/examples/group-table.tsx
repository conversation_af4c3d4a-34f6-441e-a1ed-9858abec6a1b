import { useTableState, useTableInstance, getColumnHeader } from './hooks/hooks'
import { GroupingControls } from '@/components/table/GroupingControls'
import { TableContainer } from '@/components/table/TableContainer'

// ==================== 主组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, columnOrder, setColumnOrder, toggleGrouping } = useTableState()
  const table = useTableInstance(data, grouping, columnOrder, setGrouping, setColumnOrder)
  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      <GroupingControls
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
        getColumnHeader={getColumnHeader}
      />
      <TableContainer table={table} grouping={grouping} rows={rows} />
    </div>
  )
}

export default GroupableTaskTable
