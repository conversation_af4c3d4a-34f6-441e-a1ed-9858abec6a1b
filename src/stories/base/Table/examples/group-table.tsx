import React from 'react'
import { useTableState, useTableInstance, getColumnHeader } from './hooks/hooks'
import { TableContainer } from '@/components/table/TableContainer'
import classNames from 'classnames'

// ==================== 分组控制组件（业务层封装）====================
const BusinessGroupingControls: React.FC<{
  grouping: string[]
  groupableColumns: string[]
  toggleGrouping: (columnId: string) => void
  setGrouping: (grouping: string[]) => void
}> = ({ grouping, groupableColumns, toggleGrouping, setGrouping }) => {
  return (
    <div className='mb-6 p-4 bg-gray-50 rounded-lg border'>
      <h3 className='text-lg font-semibold text-gray-900 mb-4'>任务列表 - 分组控制</h3>

      <div className='flex flex-wrap gap-2 mb-4'>
        <span className='text-sm font-medium text-gray-700 flex items-center'>
          选择分组:
        </span>
        {groupableColumns.map((colId) => (
          <button
            key={colId}
            onClick={() => toggleGrouping(colId)}
            className={classNames(
              'px-3 py-1 text-sm rounded-md transition-colors',
              {
                'bg-blue-600 text-white hover:bg-blue-700': grouping.includes(colId),
                'bg-gray-200 text-gray-700 hover:bg-gray-300': !grouping.includes(colId),
              }
            )}
          >
            {getColumnHeader(colId)}
            {grouping.includes(colId) && ' ✓'}
          </button>
        ))}
      </div>

      {grouping.length > 0 && (
        <div className='flex flex-wrap gap-2 mb-4'>
          <span className='text-sm font-medium text-gray-700 flex items-center'>
            当前分组顺序:
          </span>
          {grouping.map((colId, index) => (
            <div
              key={colId}
              className='flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm'
            >
              <span className='mr-2'>
                {index + 1}. {getColumnHeader(colId)}
              </span>
              <button
                onClick={() => toggleGrouping(colId)}
                className='text-blue-600 hover:text-blue-800 font-bold'
                title='移除此分组'
              >
                ×
              </button>
            </div>
          ))}
          <button
            onClick={() => setGrouping([])}
            className='px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors'
          >
            清除所有分组
          </button>
        </div>
      )}

      {grouping.length === 0 && (
        <div className='text-sm text-gray-500 italic'>
          点击上方按钮选择要分组的列
        </div>
      )}
    </div>
  )
}

// ==================== 主业务组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, columnOrder, setColumnOrder, toggleGrouping } = useTableState()
  const table = useTableInstance(data, grouping, columnOrder, setGrouping, setColumnOrder)
  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      {/* 业务层分组控制 */}
      <BusinessGroupingControls
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
      />

      {/* 融入TableContainer的表格渲染 */}
      <div className='bg-white rounded-lg shadow-sm border'>
        <TableContainer
          table={table}
          grouping={grouping}
          rows={rows}
        />
      </div>
    </div>
  )
}

export default GroupableTaskTable
