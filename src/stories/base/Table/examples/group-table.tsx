import { useTableState, getColumnHeader } from '@/stories/base/Table/hooks'
import { GroupingControls } from '@/components/table/GroupingControls'
import Table from '@/stories/base/Table'
import { columns } from './group/columns'
import { defaultData } from './group/data'

// ==================== 主组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, toggleGrouping } = useTableState(defaultData, columns)
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      <GroupingControls
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
        getColumnHeader={(colId: string) => getColumnHeader(colId, columns)}
      />

      {/* 使用新的 Table 组件，启用分组模式 */}
      <Table
        columns={columns}
        dataSource={data}
        grouping={grouping}
        onGroupingChange={setGrouping}
        enableGrouping={true}
        useGroupingMode={true}
      />
    </div>
  )
}

export default GroupableTaskTable
