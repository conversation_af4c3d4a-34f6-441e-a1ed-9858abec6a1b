import React from 'react'
import { TableRow } from './TableRow'

// ==================== 表体组件（纯组件）====================
interface TableBodyProps {
  table: any
  grouping: string[]
  rows: any[]
}

export const TableBody: React.FC<TableBodyProps> = ({ 
  table, 
  grouping, 
  rows 
}) => {
  // 工具函数：判断指定分组的父层级是否为最后一个
  const isParentLastInGrandParent = (
    currentGroupRow: any,
    currentRowIndex: number
  ) => {
    // 找到父分组
    let parentGroup = null
    for (let i = currentRowIndex - 1; i >= 0; i--) {
      const prevRow = rows[i]
      if (
        prevRow.getIsGrouped() &&
        prevRow.depth === currentGroupRow.depth - 1
      ) {
        parentGroup = prevRow
        break
      }
    }

    if (!parentGroup) return false

    // 查找父分组的下一个同级分组
    for (let i = currentRowIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i]
      if (
        nextRow.getIsGrouped() &&
        nextRow.depth === parentGroup.depth
      ) {
        return false
      }
      if (
        nextRow.getIsGrouped() &&
        nextRow.depth < parentGroup.depth
      ) {
        break
      }
    }
    return true
  }

  // 预先计算每个分组的结束位置
  const groupEndPositions = new Map()
  for (let i = 0; i < rows.length; i++) {
    const currentRow = rows[i]
    if (currentRow.getIsGrouped()) {
      let endPosition = i
      for (let j = i + 1; j < rows.length; j++) {
        const nextRow = rows[j]
        if (
          nextRow.getIsGrouped() &&
          nextRow.depth <= currentRow.depth
        ) {
          break
        }
        if (nextRow.depth > currentRow.depth) {
          endPosition = j
        }
      }
      groupEndPositions.set(currentRow.id, endPosition)
    }
  }

  return (
    <tbody>
      {grouping.length > 0 && <div className='w-full pt-1.5'></div>}
      {rows.map((row: any, rowIndex: number) => (
        <TableRow
          key={row.id}
          row={row}
          rowIndex={rowIndex}
          rows={rows}
          table={table}
          isParentLastInGrandParent={isParentLastInGrandParent}
          groupEndPositions={groupEndPositions}
        />
      ))}
    </tbody>
  )
}
