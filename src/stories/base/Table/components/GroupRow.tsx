import React from 'react'
import { GroupRowProps } from './types'
import { getBorderRadius } from './utils'
import { GroupEndRows } from './GroupEndRows'

// ==================== 分组行组件（纯组件）====================
export const GroupRow: React.FC<GroupRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  groupEndPositions,
}) => {
  const borderRadius = getBorderRadius(row.depth)
  const isExpanded = row.getIsExpanded()

  // 判断是否为第一层分组的最后一个分组
  const isLastFirstLevelGroup = (() => {
    if (row.depth !== 0) return false
    const firstLevelGroups = rows.filter(
      (r: any) => r.getIsGrouped() && r.depth === 0
    )
    const currentGroupIndex = firstLevelGroups.findIndex(
      (g: any) => g.id === row.id
    )
    return currentGroupIndex === firstLevelGroups.length - 1
  })()

  // 判断是否为父分组内的最后一个子分组
  const isLastSubGroupInParent = (() => {
    if (row.depth === 0) return false
    for (let i = rowIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i]
      if (nextRow.getIsGrouped()) {
        if (nextRow.depth < row.depth) {
          return true
        }
        if (nextRow.depth === row.depth) {
          return false
        }
      }
    }
    return true
  })()

  return (
    <tr
      key={row.id}
      style={{
        height: '48px', // 🔧 固定分组行高度
        minHeight: '48px',
      }}
    >
      <td
        colSpan={table.getAllLeafColumns().length}
        className='p-0'
        style={{
          height: '48px', // 🔧 确保单元格高度一致
          verticalAlign: 'middle',
        }}
      >
        <div
          className='flex items-center font-medium text-gray-700 w-full py-2 px-3'
          style={{
            backgroundColor: '#F0F4F8',
            borderRadius: borderRadius,
            marginLeft: `${row.depth * 24}px`,
            marginRight: `${row.depth * 24}px`,
            border: '1px solid #DFE3EA',
            height: '40px', // 🔧 内容区域固定高度
            minHeight: '40px',
          }}
        >
          <button
            onClick={row.getToggleExpandedHandler()}
            className='text-sm p-1 mr-2 hover:bg-gray-200 rounded flex-shrink-0'
          >
            {isExpanded ? '▼' : '▶'}
          </button>
          <span className='text-xs font-normal bg-white text-gray-600 px-2 py-1 rounded border flex-shrink-0'>
            L{row.depth + 1}
          </span>
          <span className='ml-2 flex-1 truncate'>
            {String(row.groupingValue)}
          </span>
          <span className='text-gray-500 font-normal text-xs bg-white px-2 py-1 rounded border flex-shrink-0'>
            {row.subRows.length}条
          </span>
        </div>
      </td>
    </tr>
  )
}
