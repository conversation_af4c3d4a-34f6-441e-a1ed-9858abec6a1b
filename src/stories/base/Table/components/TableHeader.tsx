import React from 'react'
import { flexRender } from '@tanstack/react-table'

import { Virtualizer } from '@tanstack/react-virtual'
import { Table } from '@tanstack/react-table'

// ==================== 表头组件（纯组件）====================
interface TableHeaderProps {
  columnVirtualizer: Virtualizer<HTMLDivElement, HTMLTableCellElement>
  table: Table<any>
  grouping: string[]
}

export const TableHeader: React.FC<TableHeaderProps> = ({ table, grouping }) => {
  return (
    <thead className='align-top'>
      {table.getHeaderGroups().map((headerGroup: any) => (
        <tr
          key={headerGroup.id}
          style={{ backgroundColor: '#F7F9FC' }}
        >
          {headerGroup.headers.map((header: any, index: number) => (
            <th
              key={header.id}
              colSpan={header.colSpan}
              className={`px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider
                ${index === 0 ? 'rounded-tl-lg border-l' : ''}
                ${index === headerGroup.headers.length - 1 ? 'rounded-tr-lg border-r' : ''}
                ${index === 0 && grouping.length > 0 ? 'rounded-bl-lg' : ''}
                ${index === headerGroup.headers.length - 1 && grouping.length > 0 ? 'rounded-br-lg' : ''}
                ${index !== 0 && index !== headerGroup.headers.length - 1 ? 'border-l border-r' : ''}
                border-t border-b border-solid border-[#DFE3EA]`}
            >
              {header.isPlaceholder
                ? null
                : flexRender(header.column.columnDef.header, header.getContext())}
            </th>
          ))}
        </tr>
      ))}
    </thead>
  )
}
