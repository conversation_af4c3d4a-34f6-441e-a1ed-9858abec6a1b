import React from 'react'
import { DataRowEndSpacingProps } from './types'

// ==================== 数据行结束间距组件（纯组件）====================
export const DataRowEndSpacing: React.FC<DataRowEndSpacingProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  // 判断是否为分组内的最后一行数据
  const isLastDataRowInGroup = (() => {
    if (rowIndex === rows.length - 1) return true
    const nextRow = rows[rowIndex + 1]
    if (!nextRow) return true
    if (!row.getIsGrouped() && nextRow.getIsGrouped()) return true
    return false
  })()

  // 小于等于2层级的最后一行数据行后添加tr
  const shouldAddSpacing = row.depth <= 2 && isLastDataRowInGroup

  // 检查当前数据行是否是某个分组的最后一个元素
  const groupsToEnd = []
  for (const [groupId, endPosition] of groupEndPositions) {
    if (endPosition === rowIndex) {
      const groupRow = rows.find((r: any) => r.id === groupId)
      if (groupRow && groupRow.depth > 1) {
        const parentIsLast = isParentLastInGrandParent(groupRow, rowIndex)
        groupsToEnd.push({ groupRow, parentIsLast })
      }
    }
  }

  return (
    <>
      {shouldAddSpacing && (
        <tr>
          <td
            colSpan={table.getAllLeafColumns().length}
            className={`p-0 ${
              row.depth === 2 && isParentLastInGrandParent(row, rowIndex) ? 'pb-2' : ''
            }`}
          >
            {(() => {
              if (row.depth === 1) {
                return <div className='h-2' />
              } else if (row.depth === 2) {
                const parentIsLast = isParentLastInGrandParent(row, rowIndex)
                if (parentIsLast) {
                  return <div className='h-2 border-l border-r border-b border-solid border-[#DFE3EA] rounded-br-[8px] rounded-bl-[8px]' />
                } else {
                  return <div className='h-2 border-l border-r border-solid border-[#DFE3EA]' />
                }
              }
            })()}
          </td>
        </tr>
      )}

      {/* 分组结束的占位边框 */}
      {groupsToEnd.map((item, gIndex) => (
        <React.Fragment key={`group-end-${item.groupRow.id}`}>
          <tr>
            <td
              colSpan={table.getAllLeafColumns().length}
              className={`p-0 border-solid border-[#DFE3EA] ${
                row.depth < 0
                  ? 'border-l border-r'
                  : 'border-l border-r'
              }`}
            >
              <div className='flex w-full'>
                {Array.from({
                  length: row.depth - gIndex - 2,
                }).map((_, boxIndex) => (
                  <div
                    key={boxIndex}
                    className={`${
                      boxIndex < 1 ? '' : 'border-l'
                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                  />
                ))}
                <div
                  className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                    row.depth < 0 ? 'mx-6' : ''
                  }`}
                  style={{
                    height: `8px`,
                    borderRadius: `0 0 ${6 + row.depth}px ${
                      6 + row.depth
                    }px`,
                  }}
                />
                {Array.from({
                  length: row.depth - gIndex - 2,
                }).map((_, boxIndex) => (
                  <div
                    key={boxIndex}
                    className={`${
                      boxIndex < 1 ? '' : 'border-r'
                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                  />
                ))}
              </div>
            </td>
          </tr>
          <tr className='h-2'>
            <td 
              className={`h-full p-0 ${item.parentIsLast ? 'pb-2' : ''}`} 
              colSpan={5}
            >
              <div
                className={`border-solid border-[#DFE3EA] border-l border-r w-full h-[8px] ${
                  item.parentIsLast ? 'border-b' : ''
                }`}
                style={{
                  borderRadius: item.parentIsLast 
                    ? `0 0 ${6 + row.depth}px ${6 + row.depth}px`
                    : 'none',
                }}
              />
            </td>
          </tr>
        </React.Fragment>
      ))}
    </>
  )
}
