import React from 'react'

// ==================== 表格状态管理 Hook ====================
export const useTableState = <T = any>(initialData: T[] = [], initialGrouping: string[] = []) => {
  const [data] = React.useState<T[]>(() => [...initialData])
  const [grouping, setGrouping] = React.useState<string[]>(initialGrouping)
  const [columnOrder, setColumnOrder] = React.useState<string[]>([])

  const toggleGrouping = (columnId: string) => {
    setGrouping((prevGrouping) => {
      if (prevGrouping.includes(columnId)) {
        return prevGrouping.filter((id) => id !== columnId)
      }
      return [...prevGrouping, columnId]
    })
  }

  return {
    data,
    grouping,
    setGrouping,
    columnOrder,
    setColumnOrder,
    toggleGrouping,
  }
}
