import React from 'react'
import {
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'

export const useTableState = (defaultData: any[], columns: any[]) => {
  const [data] = React.useState<any[]>(() => [...defaultData])
  const [grouping, setGrouping] = React.useState<string[]>(['status', 'priority'])
  const [columnOrder, setColumnOrder] = React.useState<string[]>(
    columns.map((column) => column.id || '')
  )

  const toggleGrouping = (columnId: string) => {
    setGrouping((prevGrouping) => {
      if (prevGrouping.includes(columnId)) {
        return prevGrouping.filter((id) => id !== columnId)
      }
      return [...prevGrouping, columnId]
    })
  }

  return {
    data,
    grouping,
    setGrouping,
    columnOrder,
    setColumnOrder,
    toggleGrouping,
  }
}

export const useTableInstance = (
  data: any[], 
  columns: any[],
  grouping: string[], 
  columnOrder: string[], 
  setGrouping: any, 
  setColumnOrder: any
) => {
  return useReactTable({
    data,
    columns,
    state: {
      grouping,
      columnOrder,
    },
    groupedColumnMode: false,
    onGroupingChange: setGrouping,
    onColumnOrderChange: setColumnOrder,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
  })
}

// 获取列标题的业务逻辑
export const getColumnHeader = (colId: string, columns: any[]) => {
  const column = columns.find((col) => col.id === colId)
  return typeof column?.header === 'string' ? column.header : colId
}
