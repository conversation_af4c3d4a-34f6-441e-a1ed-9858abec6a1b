import {
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'

// export const useTableInstance = (
//   data: any[], 
//   grouping: string[], 
//   columnOrder: string[], 
//   setGrouping: any, 
//   setColumnOrder: any,
//   columns: any[],
//   props: any
// ) => {
//   return useReactTable({
//     data,
//     columns,
//     state: {
//       grouping,
//       columnOrder,
//     },
//     groupedColumnMode: false,
//     onGroupingChange: setGrouping,
//     onColumnOrderChange: setColumnOrder,
//     getExpandedRowModel: getExpandedRowModel(),
//     getGroupedRowModel: getGroupedRowModel(),
//     getCoreRowModel: getCoreRowModel(),
//     ...props,
//   })
// }

// 获取列标题的业务逻辑
export const getColumnHeader = (colId: string, columns: any[]) => {
  const column = columns.find((col) => col.id === colId)
  return typeof column?.header === 'string' ? column.header : colId
}
