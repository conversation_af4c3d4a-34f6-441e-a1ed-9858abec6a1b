// ==================== 表格工具函数 ====================

/**
 * 获取列标题的显示文本
 */
export const getColumnHeader = (colId: string, columns: any[]): string => {
  const column = columns.find((col) => col.id === colId || col.accessorKey === colId)
  if (column?.header) {
    return typeof column.header === 'string' ? column.header : colId
  }
  return colId
}

/**
 * 计算分组边框半径
 */
export const getBorderRadius = (depth: number): string => {
  switch (depth) {
    case 0: return '8px'
    case 1: return '6px'
    case 2: return '4px'
    default: return '4px'
  }
}

/**
 * 计算每个分组的结束位置
 */
export const calculateGroupEndPositions = (rows: any[]): Map<string, number> => {
  const positions = new Map()
  
  for (let i = 0; i < rows.length; i++) {
    const currentRow = rows[i]
    if (currentRow.getIsGrouped()) {
      let endPosition = i
      for (let j = i + 1; j < rows.length; j++) {
        const nextRow = rows[j]
        if (
          nextRow.getIsGrouped() &&
          nextRow.depth <= currentRow.depth
        ) {
          break
        }
        if (nextRow.depth > currentRow.depth) {
          endPosition = j
        }
      }
      positions.set(currentRow.id, endPosition)
    }
  }
  
  return positions
}

/**
 * 判断是否为父级分组中的最后一个子项
 */
export const isParentLastInGrandParent = (row: any, index: number, rows: any[]): boolean => {
  if (index >= rows.length - 1) return true
  
  const nextRow = rows[index + 1]
  if (!nextRow) return true
  
  // 如果下一行是分组行且层级小于等于当前行，说明当前行是最后一个
  if (nextRow.getIsGrouped() && nextRow.depth <= row.depth) {
    return true
  }
  
  return false
}
