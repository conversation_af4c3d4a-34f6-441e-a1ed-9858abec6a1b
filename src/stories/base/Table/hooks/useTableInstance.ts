import {
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'

// ==================== 表格实例化 Hook ====================
export const useTableInstance = <T = any>(
  data: T[], 
  columns: any[],
  grouping: string[], 
  columnOrder: string[], 
  setGrouping: any, 
  setColumnOrder: any
) => {
  return useReactTable({
    data,
    columns,
    state: {
      grouping,
      columnOrder,
    },
    groupedColumnMode: false,
    onGroupingChange: setGrouping,
    onColumnOrderChange: setColumnOrder,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
  })
}
