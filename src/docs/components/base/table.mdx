# Table 表格 组件

`Table` 组件的文档

## 表格分组

```js type=case file=@/stories/base/Table/examples/group-table.tsx

```

## 基础用法

```js type=component file=@/stories/base/Table/index.tsx

```

## 基础案例

```js type=case file=@/stories/base/Table/examples/base.tsx

```



## columnDnd

```js type=case file=@/stories/base/Table/examples/columnDnd.tsx

```


## virtualized

{/* ```js type=case file=@/stories/base/Table/examples/virtualized.tsx

``` */}